# =============================================================================
# Painel de Spiders – BROBOT 5.4 🚀 “MEGA‑HEXAWOW” edition                  #
# =============================================================================
# Novidades 5.4 (Drill‑down por Spider 🏹):
#   • Novo painel dentro da aba **🔍 Spider** que mostra a DISTRIBUIÇÃO DE
#     TIPOS DE ERRO para a spider selecionada – tabela + gráfico dinâmico.
#   • Mantém tabelas full‑width (550 px) e hovers interativos em todas as
#     visualizações.
# =============================================================================

import streamlit as st
import pandas as pd
import altair as alt
import plotly.express as px
import csv, io, re, base64, sys, os, zipfile, gzip
from datetime import timedelta

# 🛑  Aumenta limite de tamanho de campo CSV (padrão = 128 kB)
csv.field_size_limit(500 * 1024 * 1024)  # 500 MB

###############################################################################
# 🎨  Config                                                                   #
###############################################################################

st.set_page_config(page_title="BROBOT • Spiders Dashboard", page_icon="🕷️", layout="wide")

UPLOAD_LIMIT = int(os.environ.get("STREAMLIT_SERVER_MAX_UPLOAD_SIZE", 2048))
TABLE_HEIGHT = 550  # altura padrão para TODAS as st.dataframe

PALETTE = [
    "#636EFA", "#EF553B", "#00CC96", "#AB63FA", "#FFA15A", "#19D3F3", "#FF6692", "#B6E880"
]
alt.theme.enable("default")

SOLUTIONS = {
    "SELENIUM_FAIL": "🔧 Atualizar driver/seletores",
    "WRONG_CREDENTIALS": "🔑 Revisar credenciais",
    "CAPTCHA_INCORRECTLY_SOLVED": "🤖 Ver serviço de Captcha",
    "CAPTCHA_NOT_LOADED": "⏳ Retry com back‑off",
    "INVALID_REQUEST_PARAMS": "📄 Conferir payload",
    "SELENIUM_TIMEOUT": "⏰ Aumentar timeouts",
    "LOG_ERROR": "👀 Ver logs",
    "―": "—",
}

CSV_RE = re.compile(r"(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) \| ([^|]+) \|")

###############################################################################
# 🗜️  Helpers de compactação                                                   #
###############################################################################

def _open_as_buffer(uploaded) -> io.BufferedReader:
    """Retorna objeto parecido com arquivo de texto a partir de csv|zip|gz."""
    fname = uploaded.name.lower()
    if fname.endswith(".gz"):
        return io.BytesIO(gzip.decompress(uploaded.read()))
    if fname.endswith(".zip"):
        zf = zipfile.ZipFile(uploaded)
        inner = zf.namelist()[0]
        return io.BytesIO(zf.read(inner))
    uploaded.seek(0)
    return uploaded  # csv puro

###############################################################################
# ⚙️  CSV Reader otimizado                                                     #
###############################################################################

@st.cache_data(show_spinner=False)
def parse_log(buffer: io.BufferedReader) -> pd.DataFrame:
    """Lê csv gigante em streaming ou via PyArrow."""
    try:
        return _parse_with_pyarrow(buffer)
    except Exception:
        buffer.seek(0)
        return _parse_stream(buffer)


def _parse_stream(f) -> pd.DataFrame:
    content = io.TextIOWrapper(f, encoding="utf-8", errors="ignore")
    reader = csv.reader(content)
    _ = next(reader, None)  # header
    parsed, count = [], 0
    pbar = st.progress(0, text="Lendo CSV…")
    for row in reader:
        if len(row) < 4:
            continue
        msg = ",".join(row[3:]).replace('""', '"')
        m = CSV_RE.search(msg)
        if not m:
            continue
        date_only, spider = m.group(1).split()[0], m.group(2).strip()
        is_error, error_type, scrape_id = _detect_error(msg)
        parsed.append((date_only, spider, is_error, error_type, scrape_id))
        count += 1
        if count % 10000 == 0:
            pbar.progress(min(count / 600000, 1.0))
    pbar.empty()
    df = pd.DataFrame(parsed, columns=["date", "spider", "is_error", "error_type", "scrape_id"])
    df["date"] = pd.to_datetime(df["date"]).dt.date
    return df


def _parse_with_pyarrow(f):
    import pyarrow.csv as pacsv, pyarrow as pa

    f.seek(0)
    buf = pa.input_stream(f.read())
    tbl = pacsv.read_csv(buf, read_options=pacsv.ReadOptions(block_size=1 << 24))
    msgs = tbl.column("Message").to_pylist()
    parsed = []
    for msg in msgs:
        msg = str(msg).replace('""', '"')
        m = CSV_RE.search(msg)
        if not m:
            continue
        date_only, spider = m.group(1).split()[0], m.group(2).strip()
        is_error, error_type, scrape_id = _detect_error(msg)
        parsed.append((date_only, spider, is_error, error_type, scrape_id))
    df = pd.DataFrame(parsed, columns=["date", "spider", "is_error", "error_type", "scrape_id"])
    df["date"] = pd.to_datetime(df["date"]).dt.date
    return df


def _detect_error(msg: str):
    et_match = re.search(r'"error_type"\s*:\s*"([^"/]+)"', msg)
    log_err_match = re.search(r'"log_count_error"\s*:\s*(\d+)', msg)
    scrape_id_match = re.search(r'"scrape_id"\s*:\s*"?([^",\s}]+)"?', msg)

    log_err_cnt = int(log_err_match.group(1)) if log_err_match else 0
    is_error = bool(et_match) or log_err_cnt > 0
    error_type = et_match.group(1) if et_match else ("LOG_ERROR" if log_err_cnt > 0 else "―")
    scrape_id = scrape_id_match.group(1) if scrape_id_match else "—"

    return is_error, error_type, scrape_id

###############################################################################
# 📁 Upload                                                                   #
###############################################################################

st.sidebar.header("📁 Carregar log (≤2048 MB) – aceita CSV/GZ/ZIP")
file = st.sidebar.file_uploader("Arraste aqui", type=["csv", "gz", "zip"])
if not file:
    if UPLOAD_LIMIT < 2048:
        st.sidebar.warning(f"⚠️ Limite atual de upload é {UPLOAD_LIMIT} MB. Configure server.maxUploadSize ≥ 2048 MB ou compacte em .gz/.zip.")
    st.sidebar.info("⬆️ Envie o arquivo para começar")
    st.stop()

size_mb = file.size / 1_048_576
if size_mb > 2048:
    st.error(f"Arquivo com {size_mb:.1f} MB excede o limite de 2048 MB.")
    st.stop()

buffer = _open_as_buffer(file)

with st.spinner("Processando arquivo…"):
    df = parse_log(buffer)
if df.empty:
    st.error("Nenhum dado válido encontrado.")
    st.stop()

###############################################################################
# 🗓️ Filtros                                                                  #
###############################################################################

min_day, max_day = df.date.min(), df.date.max()
period = st.sidebar.selectbox("Período", ["7 dias", "15 dias", "30 dias", "Todo período"])
ndays = {"7 dias": 7, "15 dias": 15, "30 dias": 30, "Todo período": (max_day - min_day).days}[period]
start_date = max_day - timedelta(days=ndays)
spiders = sorted(df.spider.unique())
sel_spiders = st.sidebar.multiselect("Spiders", spiders, default=spiders)
filtered = df[(df.date >= start_date) & (df.date <= max_day) & df.spider.isin(sel_spiders)]

###############################################################################
# KPIs                                                                        #
###############################################################################

tot, err = len(filtered), int(filtered.is_error.sum())
succ_pct = 100 * (tot - err) / max(tot, 1)
col1, col2, col3 = st.columns(3)
col1.metric("Consultas", f"{tot:,}")
col2.metric("Falhas", f"{err:,}", f"{succ_pct:.1f}% sucesso")
col3.metric("Spiders ativos", f"{len(sel_spiders)} / {len(spiders)}")

###############################################################################
# Tabs                                                                        #
###############################################################################

overview, errors_tab, spider_tab, simplified_tab, raw = st.tabs(["📊 Overview", "🚑 Erros", "🔍 Spider", "🔧 Simplificado", "📝 Raw"])

###############################################################################
# 1️⃣ Overview                                                                #
###############################################################################

with overview:
    daily = filtered.groupby(["date", "spider"]).size().reset_index(name="consultas")
    st.altair_chart(
        alt.Chart(daily)
        .mark_area(opacity=0.7)
        .encode(
            x="date:T",
            y="sum(consultas):Q",
            color=alt.Color("spider:N", scale=alt.Scale(range=PALETTE)),
        ),
        use_container_width=True,
    )

    # Heatmap de falhas -------------------------------------------------------
    err_heat = filtered[filtered.is_error].groupby(["date", "spider"]).size().reset_index(name="falhas")
    if not err_heat.empty:
        st.altair_chart(
            alt.Chart(err_heat)
            .mark_rect()
            .encode(
                x="date:T",
                y=alt.Y("spider:N", sort="-x"),
                color=alt.Color("falhas:Q", scale=alt.Scale(scheme="reds"), legend=None),
                tooltip=["date", "spider", "falhas"],
            )
            .properties(height=300),
            use_container_width=True,
        )

    agg = filtered.groupby("spider").agg(total=("spider", "size"), falhas=("is_error", "sum")).reset_index()
    agg["sucesso_pct"] = ((agg.total - agg.falhas) / agg.total * 100).round(1)

    hover = alt.selection_single(fields=["spider"], on="mouseover", empty="none")

    bar = (
        alt.Chart(agg)
        .mark_bar()
        .encode(
            y=alt.Y("spider:N", sort="-x"),
            x="sucesso_pct:Q",
            tooltip=["total", "falhas", "sucesso_pct"],
            color=alt.Color(
                "sucesso_pct:Q",
                scale=alt.Scale(domain=[0, 95, 100], range=["#EF553B", "#FFA15A", "#00CC96"]),
                legend=alt.Legend(title="% Sucesso"),
            ),
            opacity=alt.condition(hover, alt.value(1), alt.value(0.6)),
        )
        .add_selection(hover)
    )

    labels = bar.mark_text(align="left", dx=3).encode(text="sucesso_pct:Q", opacity=alt.condition(hover, alt.value(1), alt.value(0)))

    st.altair_chart(bar + labels, use_container_width=True)

###############################################################################
# 2️⃣ Erros                                                                   #
###############################################################################

with errors_tab:
    if err == 0:
        st.success("Sem falhas! 🎉")
    else:
        # ── Tabela de tipos de erro ──────────────────────────────────────────
        err_tbl = (
            filtered[filtered.is_error]
            .groupby("error_type")
            .size()
            .reset_index(name="ocorrências")
            .assign(solução=lambda d: d.error_type.map(SOLUTIONS).fillna("—"))
            .sort_values("ocorrências", ascending=False)
        )
        st.dataframe(err_tbl, hide_index=True, height=TABLE_HEIGHT, use_container_width=True)
        st.plotly_chart(
            px.pie(err_tbl, names="error_type", values="ocorrências", hole=0.4, color_discrete_sequence=PALETTE),
            use_container_width=True,
        )

        # ── Falhas por Spider (com %) ────────────────────────────────────────
        err_spider_tbl = (
            filtered[filtered.is_error]
            .groupby("spider")
            .size()
            .reset_index(name="falhas")
            .sort_values("falhas", ascending=False)
        )
        err_spider_tbl["falhas_pct"] = (err_spider_tbl.falhas / err * 100).round(1)

        st.subheader("Falhas por Spider")
        st.dataframe(err_spider_tbl, hide_index=True, height=TABLE_HEIGHT, use_container_width=True)

        hover2 = alt.selection_single(fields=["spider"], on="mouseover", empty="none")

        bar2 = (
            alt.Chart(err_spider_tbl)
            .mark_bar(color="#EF553B")
            .encode(
                y=alt.Y("spider:N", sort="-x"),
                x=alt.X("falhas:Q", title="Falhas"),
                tooltip=["falhas", "falhas_pct"],
                opacity=alt.condition(hover2, alt.value(1), alt.value(0.7)),
            )
            .add_selection(hover2)
        )
        labels2 = bar2.mark_text(align="left", dx=3).encode(text="falhas:Q", opacity=alt.condition(hover2, alt.value(1), alt.value(0)))

        st.altair_chart(bar2 + labels2, use_container_width=True)

###############################################################################
# 3️⃣ Spider individual – AGORA COM DRILL‑DOWN DE ERROS                      #
###############################################################################

with spider_tab:
    sel = st.selectbox("Spider", spiders)
    sp = filtered[filtered.spider == sel]
    if sp.empty:
        st.info("Sem registros.")
    else:
        # ── Métricas da Spider ──────────────────────────────────────────────
        total_consultas = len(sp)
        total_erros = int(sp.is_error.sum())
        total_sucessos = total_consultas - total_erros
        taxa_sucesso = (total_sucessos / total_consultas * 100) if total_consultas > 0 else 0

        col1, col2, col3, col4 = st.columns(4)
        col1.metric("Total Consultas", f"{total_consultas:,}")
        col2.metric("Sucessos", f"{total_sucessos:,}", f"{taxa_sucesso:.1f}%")
        col3.metric("Falhas", f"{total_erros:,}")
        col4.metric("Taxa de Sucesso", f"{taxa_sucesso:.1f}%")

        # ── Gráfico de consultas por dia ────────────────────────────────────
        daily_data = sp.groupby(["date", "is_error"]).size().reset_index(name="count")
        daily_data["status"] = daily_data["is_error"].map({True: "Falhas", False: "Sucessos"})

        st.altair_chart(
            alt.Chart(daily_data)
            .mark_area(opacity=0.7)
            .encode(
                x="date:T",
                y="count:Q",
                color=alt.Color("status:N", scale=alt.Scale(domain=["Sucessos", "Falhas"], range=["#00CC96", "#EF553B"])),
                tooltip=["date", "status", "count"]
            ),
            use_container_width=True,
        )

        # ── Drill‑down: distribuição de erros por tipo ─────────────────────
        err_types = (
            sp[sp.is_error]
            .groupby("error_type")
            .size()
            .reset_index(name="ocorrências")
            .assign(solução=lambda d: d.error_type.map(SOLUTIONS).fillna("—"))
            .sort_values("ocorrências", ascending=False)
        )

        if err_types.empty:
            st.success("Esta spider não apresentou falhas no período! 🎉")
        else:
            st.subheader(f"Erros da spider • {sel}")
            st.dataframe(err_types, hide_index=True, use_container_width=True)

            hover3 = alt.selection_single(fields=["error_type"], on="mouseover", empty="none")

            bar3 = (
                alt.Chart(err_types)
                .mark_bar(color="#EF553B")
                .encode(
                    y=alt.Y("error_type:N", sort="-x"),
                    x="ocorrências:Q",
                    tooltip=["ocorrências", "solução"],
                    opacity=alt.condition(hover3, alt.value(1), alt.value(0.7)),
                )
                .add_selection(hover3)
            )
            labels3 = bar3.mark_text(align="left", dx=3).encode(text="ocorrências:Q", opacity=alt.condition(hover3, alt.value(1), alt.value(0)))

            st.altair_chart(bar3 + labels3, use_container_width=True)

            # Mini‑pie para destaque visual (opcional)
            st.plotly_chart(
                px.pie(err_types, names="error_type", values="ocorrências", hole=0.5, color_discrete_sequence=PALETTE),
                use_container_width=True,
            )

        # ── Resumo de Status ────────────────────────────────────────────────
        st.subheader(f"Resumo de Status • {sel}")

        # Criar dados para o resumo
        status_summary = pd.DataFrame({
            "Status": ["Sucessos", "Falhas"],
            "Quantidade": [total_sucessos, total_erros],
            "Percentual": [f"{taxa_sucesso:.1f}%", f"{100 - taxa_sucesso:.1f}%"]
        })

        # Tabela de resumo
        st.dataframe(status_summary, hide_index=True, use_container_width=True)

        # Gráfico de pizza abaixo da tabela
        if total_consultas > 0:
            st.plotly_chart(
                px.pie(status_summary, names="Status", values="Quantidade",
                       color_discrete_map={"Sucessos": "#00CC96", "Falhas": "#EF553B"},
                       hole=0.4),
                use_container_width=True,
            )

        # ── Log detalhado por status ────────────────────────────────────────
        tab1, tab2 = st.tabs(["📋 Todos os Registros", "🚨 Apenas Erros"])

        with tab1:
            # Mostrar todos os registros com status
            all_logs = sp[["date", "is_error", "scrape_id"]].copy()
            all_logs["status"] = all_logs["is_error"].map({True: "❌ Falha", False: "✅ Sucesso"})
            all_logs["error_type"] = sp["error_type"].fillna("—")
            all_logs = all_logs[["date", "scrape_id", "status", "error_type"]].rename(columns={
                "date": "Data",
                "scrape_id": "Scrape ID",
                "status": "Status",
                "error_type": "Tipo de Erro"
            })
            st.dataframe(all_logs, height=TABLE_HEIGHT, use_container_width=True)

        with tab2:
            # Mostrar apenas erros (comportamento original)
            if total_erros > 0:
                error_logs = sp[sp.is_error][["date", "scrape_id", "error_type"]].assign(
                    solução=lambda d: d.error_type.map(SOLUTIONS).fillna("—")
                ).rename(columns={
                    "date": "Data",
                    "scrape_id": "Scrape ID",
                    "error_type": "Tipo de Erro",
                    "solução": "Solução"
                })
                st.dataframe(error_logs, height=TABLE_HEIGHT, use_container_width=True)
            else:
                st.success("Nenhum erro encontrado para esta spider! 🎉")

###############################################################################
# 4️⃣ Simplificado - Exemplos para Manutenção                               #
###############################################################################

with simplified_tab:
    st.subheader("🔧 Exemplos de Erros por Spider")
    st.info("Esta tabela mostra um exemplo de cada tipo de erro por spider para facilitar a criação de atividades de manutenção.")

    # Filtrar apenas registros com erro
    error_data = filtered[filtered.is_error].copy()

    if error_data.empty:
        st.success("🎉 Nenhum erro encontrado no período selecionado!")
    else:
        # Agrupar por spider e error_type, pegando o primeiro exemplo de cada combinação
        simplified_errors = (
            error_data.groupby(['spider', 'error_type'])
            .agg({
                'scrape_id': 'first',  # Pega o primeiro scrape_id como exemplo
                'date': 'first'        # Pega a primeira data como referência
            })
            .reset_index()
        )

        # Adicionar coluna de solução
        simplified_errors['solução'] = simplified_errors['error_type'].map(SOLUTIONS).fillna("—")

        # Reordenar colunas e renomear
        simplified_errors = simplified_errors[['spider', 'error_type', 'scrape_id', 'date', 'solução']].rename(columns={
            'spider': 'Spider',
            'error_type': 'Tipo de Erro',
            'scrape_id': 'Scrape ID (Exemplo)',
            'date': 'Data do Exemplo',
            'solução': 'Solução Sugerida'
        })

        # Ordenar por spider e tipo de erro
        simplified_errors = simplified_errors.sort_values(['Spider', 'Tipo de Erro'])

        # Mostrar métricas
        col1, col2, col3 = st.columns(3)
        col1.metric("Spiders com Erro", simplified_errors['Spider'].nunique())
        col2.metric("Tipos de Erro Únicos", simplified_errors['Tipo de Erro'].nunique())
        col3.metric("Total de Combinações", len(simplified_errors))

        # Tabela principal
        st.dataframe(
            simplified_errors,
            hide_index=True,
            height=TABLE_HEIGHT,
            use_container_width=True
        )

        # Botão para download CSV
        csv_data = simplified_errors.to_csv(index=False)
        st.download_button(
            label="📥 Download CSV para Manutenção",
            data=csv_data,
            file_name=f"erros_simplificados_{filtered.date.min()}_{filtered.date.max()}.csv",
            mime="text/csv"
        )

###############################################################################
# 5️⃣ Raw                                                                    #
###############################################################################

with raw:
    st.dataframe(filtered.head(200), height=TABLE_HEIGHT, use_container_width=True)
    csv_dl = base64.b64encode(filtered.to_csv(index=False).encode()).decode()
    st.markdown(
        f'<a href="data:text/csv;base64,{csv_dl}" download="logs_filtrados.csv">⬇️ Download CSV</a>',
        unsafe_allow_html=True,
    )

st.caption("BROBOT Dashboard v5.4 • feito p/ operações ⚡ • ©2025")
